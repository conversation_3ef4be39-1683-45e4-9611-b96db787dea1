import { useRef } from 'react';
import Animated<PERSON><PERSON>, { type AnimatedLogoRef } from './AnimatedLogo';
import Logo2Full from './LOGO Full Purple.svg?react';
import LogoSystems from './LOGO Systems.svg?react';

const LogoAnimationDemo = () => {
  const demo1Ref = useRef<AnimatedLogoRef>(null);
  const demo2Ref = useRef<AnimatedLogoRef>(null);
  const demo3Ref = useRef<AnimatedLogoRef>(null);
  const demo4Ref = useRef<AnimatedLogoRef>(null);

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-12 gradient-text">
          Diftra Logo Animation Demo
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Demo 1: Loading Screen Style */}
          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-4">Loading Screen Animation</h3>
            <div className="flex justify-center mb-4">
              <AnimatedLogo 
                ref={demo1Ref}
                LogoComponent={Logo2Full}
                logoProps={{ style: { height: '4rem' } }}
                duration={2500}
                staggerDelay={150}
                ease="outQuart"
                autoplay={false}
              />
            </div>
            <button 
              onClick={() => demo1Ref.current?.startAnimation()}
              className="btn-primary"
            >
              Start Animation
            </button>
            <p className="text-sm text-gray-400 mt-2">
              Smooth, elegant drawing effect perfect for loading screens
            </p>
          </div>

          {/* Demo 2: Header Style */}
          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-4">Header Hover Animation</h3>
            <div 
              className="flex justify-center mb-4 cursor-pointer"
              onMouseEnter={() => demo2Ref.current?.startAnimation()}
            >
              <AnimatedLogo 
                ref={demo2Ref}
                LogoComponent={LogoSystems}
                logoProps={{ style: { height: '3rem' } }}
                duration={1200}
                staggerDelay={60}
                ease="outQuart"
                autoplay={false}
              />
            </div>
            <p className="text-sm text-gray-400">
              Hover to trigger animation - perfect for interactive elements
            </p>
          </div>

          {/* Demo 3: Fast & Snappy */}
          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-4">Fast & Snappy</h3>
            <div className="flex justify-center mb-4">
              <AnimatedLogo 
                ref={demo3Ref}
                LogoComponent={Logo2Full}
                logoProps={{ style: { height: '3.5rem' } }}
                duration={800}
                staggerDelay={40}
                ease="outBack"
                autoplay={false}
              />
            </div>
            <button 
              onClick={() => demo3Ref.current?.startAnimation()}
              className="btn-secondary"
            >
              Quick Draw
            </button>
            <p className="text-sm text-gray-400 mt-2">
              Fast animation with bouncy easing for dynamic feel
            </p>
          </div>

          {/* Demo 4: Looping */}
          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-4">Continuous Loop</h3>
            <div className="flex justify-center mb-4">
              <AnimatedLogo 
                ref={demo4Ref}
                LogoComponent={LogoSystems}
                logoProps={{ style: { height: '3rem' } }}
                duration={3000}
                staggerDelay={100}
                ease="inOutQuad"
                autoplay={true}
                loop={true}
              />
            </div>
            <button 
              onClick={() => demo4Ref.current?.resetAnimation()}
              className="btn-outline-gradient"
            >
              Reset Animation
            </button>
            <p className="text-sm text-gray-400 mt-2">
              Continuous looping animation for attention-grabbing effects
            </p>
          </div>
        </div>

        <div className="mt-12 bg-gray-800 rounded-lg p-6">
          <h3 className="text-2xl font-semibold mb-4">Implementation Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold text-purple-400 mb-2">Features:</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• SVG path drawing animation using anime.js</li>
                <li>• Customizable duration and stagger delays</li>
                <li>• Multiple easing options</li>
                <li>• Hover triggers and manual controls</li>
                <li>• Loop and autoplay options</li>
                <li>• TypeScript support with proper refs</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-purple-400 mb-2">Usage:</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Loading screens (autoplay)</li>
                <li>• Header logos (hover trigger)</li>
                <li>• Footer animations (hover trigger)</li>
                <li>• Hero sections (attention-grabbing)</li>
                <li>• Brand showcases (looping)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoAnimationDemo;
