import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-scroll';
import { motion } from 'framer-motion';
import { FiMenu, FiX } from 'react-icons/fi';
import { BsArrowRight } from 'react-icons/bs';
import { useScrollDirection } from '../hooks/useScrollDirection';
import Animated<PERSON>ogo, { type AnimatedLogoRef } from './AnimatedLogo';
import Logo2Full from './LOGO Systems.svg?react';

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const headerLogoRef = useRef<AnimatedLogoRef>(null);

  // Only apply scroll direction behavior on mobile
  const scrollDirection = useScrollDirection({
    off: !isMobile || isOpen, // Disable when menu is open or on desktop
    thresholdPixels: 20,
  });

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Check if we're on mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is the md breakpoint in Tailwind
    };

    // Initial check
    checkIsMobile();

    // Add resize listener
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  return (
    <header
      className={`fixed left-0 top-0 z-50 w-full transition-all duration-300 ${
        isMobile
          ? 'bg-gray-900/75 backdrop-blur-lg shadow-md' // Match mobile menu styling exactly
          : (scrolled ? 'bg-gray-900/75 backdrop-blur-lg shadow-md' : 'bg-transparent')
      } ${isMobile && scrollDirection === 'down' && scrolled && !isOpen ? '-translate-y-full' : 'translate-y-0'}`}
    >
      <div className="container-custom flex h-20 items-center justify-between">
        <motion.div
          initial={{ opacity: 0.5, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="flex items-center"
        >
          {/* Animated logo that triggers on hover */}
          <span
            className="flex items-center text-2xl font-bold select-none cursor-pointer"
            onMouseEnter={() => headerLogoRef.current?.startAnimation()}
          >
            <AnimatedLogo
              ref={headerLogoRef}
              LogoComponent={Logo2Full}
              logoProps={{ style: { height: '2.5rem' }, className: 'mr-2' }}
              duration={1200}
              staggerDelay={60}
              ease="outQuart"
              autoplay={false}
            />
          </span>
        </motion.div>

        {/* Desktop Navigation */}
        <motion.nav
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="hidden space-x-8 md:flex"
        >
          <NavLinks desktop />
        </motion.nav>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="hidden md:block"
        >
          <a
            href="https://calendly.com/diftra/intro-call"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-outline-gradient group flex items-center gap-2 px-4 py-2 font-medium"
          >
            <span>Work With Us</span>
            <BsArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
          </a>
        </motion.div>

        {/* Mobile Navigation Toggle */}
        <div className="md:hidden">
          <button
            onClick={toggleMenu}
            className="rounded-md p-2 text-gray-300 hover:bg-gray-800 hover:text-white"
            aria-label="Toggle menu"
          >
            {isOpen ? <FiX size={24} /> : <FiMenu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu - Inside header with no background */}
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isOpen ? 'auto' : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{
          duration: 0.3,
          ease: [0.25, 0.1, 0.25, 1.0] // Smooth easing function
        }}
        className="md:hidden overflow-hidden"
      >
        <div className="container-custom space-y-4 py-6 landscape:py-3 landscape:space-y-2">
          <NavLinks desktop={false} onClick={() => setIsOpen(false)} />
          <div className="pt-4 landscape:pt-1 landscape:w-40 landscape:mx-auto">
            <a
              href="https://calendly.com/diftra/intro-call"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline-gradient group flex w-full items-center justify-center gap-2 px-4 py-3 font-medium landscape:py-2 text-center"
            >
              <span>Work With Us</span>
              <BsArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
            </a>
          </div>
        </div>
      </motion.div>
    </header>
  );
};

interface NavLinksProps {
  desktop: boolean;
  onClick?: () => void;
}

const NavLinks = ({ desktop, onClick }: NavLinksProps) => {
  const links = [
    { to: 'hero', label: 'Home' },
    { to: 'services', label: 'Services' },
    { to: 'why-choose-us', label: 'Why Us' },
    { to: 'process', label: 'Process' },
    { to: 'faq', label: 'FAQ' },
    { to: 'why-now', label: 'Why Now' },
    { to: 'contact', label: 'Contact' },
  ];

  if (desktop) {
    return (
      <>
        {links.map((link) => (
          <Link
            key={link.to}
            to={link.to}
            spy={true}
            smooth={true}
            offset={-80}
            duration={500}
            onClick={onClick}
            className="cursor-pointer font-medium transition-colors hover:text-purple-400 inline-block"
          >
            {link.label}
          </Link>
        ))}
      </>
    );
  }

  // Mobile layout
  return (
    <div className="portrait:block landscape:flex landscape:flex-wrap landscape:justify-between landscape:gap-1">
      {links.map((link) => (
        <Link
          key={link.to}
          to={link.to}
          spy={true}
          smooth={true}
          offset={-80}
          duration={500}
          onClick={onClick}
          className="cursor-pointer font-medium transition-colors hover:text-purple-400 block py-2 landscape:py-1 landscape:text-sm landscape:px-2"
        >
          {link.label}
        </Link>
      ))}
    </div>
  );
};

export default Header;
