import { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { animate, svg, stagger } from 'animejs';

interface AnimatedLogoProps {
  /** The SVG logo component to animate */
  LogoComponent: React.ComponentType<any>;
  /** Additional props to pass to the logo component */
  logoProps?: any;
  /** Animation duration in milliseconds */
  duration?: number;
  /** Delay between path animations in milliseconds */
  staggerDelay?: number;
  /** Whether to loop the animation */
  loop?: boolean;
  /** Whether to start animation automatically */
  autoplay?: boolean;
  /** Callback when animation completes */
  onComplete?: () => void;
  /** Animation easing function */
  ease?: string;
}

interface AnimatedLogoRef {
  startAnimation: () => void;
  resetAnimation: () => void;
}

const AnimatedLogo = forwardRef<AnimatedLogoRef, AnimatedLogoProps>(({
  LogoComponent,
  logoProps = {},
  duration = 2000,
  staggerDelay = 100,
  loop = false,
  autoplay = true,
  onComplete,
  ease = 'inOutQuad'
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<any>(null);

  // Method to manually start animation
  const startAnimation = () => {
    if (!containerRef.current) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (!svgElement) return;

    const paths = svgElement.querySelectorAll('path');
    if (paths.length === 0) return;

    try {
      const drawablePaths = svg.createDrawable(paths);

      if (animationRef.current) {
        animationRef.current.cancel();
      }

      animationRef.current = animate(drawablePaths, {
        draw: ['0 0', '0 1'],
        ease: ease,
        duration: duration,
        delay: stagger(staggerDelay),
        loop: loop,
        onComplete: onComplete
      });
    } catch (error) {
      console.warn('Failed to start animation:', error);
    }
  };

  // Method to reset animation
  const resetAnimation = () => {
    if (!containerRef.current) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (!svgElement) return;

    const paths = svgElement.querySelectorAll('path');
    if (paths.length === 0) return;

    try {
      const drawablePaths = svg.createDrawable(paths);
      drawablePaths.forEach((path: any) => {
        path.draw = '0 0';
      });
    } catch (error) {
      console.warn('Failed to reset animation:', error);
    }
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    startAnimation,
    resetAnimation
  }));

  useEffect(() => {
    if (!containerRef.current) return;

    // Small delay to ensure SVG is rendered
    const timer = setTimeout(() => {
      const svgElement = containerRef.current?.querySelector('svg');
      if (!svgElement) return;

      // Find all path elements in the SVG
      const paths = svgElement.querySelectorAll('path');
      if (paths.length === 0) return;

      try {
        // Create drawable paths using anime.js
        const drawablePaths = svg.createDrawable(paths);
        
        // Start animation if autoplay is enabled
        if (autoplay) {
          animationRef.current = animate(drawablePaths, {
            draw: ['0 0', '0 1'],
            ease: ease,
            duration: duration,
            delay: stagger(staggerDelay),
            loop: loop,
            onComplete: onComplete
          });
        }
      } catch (error) {
        console.warn('Failed to create animated logo:', error);
      }
    }, 50);

    return () => {
      clearTimeout(timer);
      if (animationRef.current) {
        animationRef.current.cancel();
      }
    };
  }, [duration, staggerDelay, loop, autoplay, onComplete, ease]);

  return (
    <div ref={containerRef} className="animated-logo">
      <LogoComponent {...logoProps} />
    </div>
  );
});

AnimatedLogo.displayName = 'AnimatedLogo';

export default AnimatedLogo;
export { type AnimatedLogoProps, type AnimatedLogoRef };
