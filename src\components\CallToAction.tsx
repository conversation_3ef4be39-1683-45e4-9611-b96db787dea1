import { motion } from 'framer-motion';
import { BsCalendar2Check } from 'react-icons/bs';

const CallToAction = () => {
  return (
    <section className="py-20 bg-gray-900 overflow-hidden relative">
      {/* Background gradient and effects */}
      {/* <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -left-40 top-20 h-80 w-80 rounded-full bg-purple-600/10 blur-[100px]" />
        <div className="absolute right-0 bottom-0 h-80 w-80 rounded-full bg-indigo-600/10 blur-[100px]" />
      </div> */}

      <div className="container-custom relative z-10">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
          layout
          className="rounded-2xl border border-gray-800 bg-gray-800/30 p-8 md:p-12 lg:p-16 backdrop-blur-sm"
        >
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
              Ready to <span className="gradient-text">Transform</span> Your Agency's Content Production?
            </h2>
            <p className="mb-8 text-xl text-gray-300">
              Schedule a free strategy call with our team and discover how a custom Content Automation Ecosystem can revolutionize your agency's operations.
            </p>
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-center md:space-x-6 md:space-y-0">
              <a
                href="https://calendly.com/diftra/intro-call"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary group flex items-center justify-center gap-2 px-8 py-4 text-lg"
              >
                <span>Book a Call</span>
                <motion.span
                className="inline-block"
                animate={{ y: [0, -4, 0] }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatDelay: 3.5
                }}
              >
                <BsCalendar2Check /> {/* Icon with animation */}
              </a>
              <a
                href="#contact"
                className="btn-secondary flex items-center justify-center px-8 py-4 text-lg"
              >
                Contact Us
              </a>
            </div>
            <p className="mt-8 text-sm text-gray-400">
              No strings attached. We'll answer your questions and provide a clear roadmap for implementation.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CallToAction;
